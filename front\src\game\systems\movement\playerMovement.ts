/**
 * Система передвижения персонажа по глобальной карте
 */

import { Position } from '../../../shared/types/Common'
import { WorldMap, WorldMapCell } from '../../../shared/types/World'
import { WorldMapDecorations } from '../../../shared/enums'
import { MOVEMENT_SPEED, WORLD_MAP_SPEED_MULTIPLIER } from '../../utils/constants/movement'
import { useGameStore } from '../../store/gameStore'

/**
 * Конвертирует значение ползунка скорости (1-5) в миллисекунды
 * 1 = 200ms (быстро), 5 = 2000ms (медленно)
 */
export function convertSpeedToMs(speedValue: number): number {
  // Ограничиваем значение от 1 до 5
  const clampedSpeed = Math.max(1, Math.min(5, speedValue))
  // Формула: 200ms + (speedValue - 1) * 450ms
  // 1 -> 200ms, 2 -> 650ms, 3 -> 1100ms, 4 -> 1550ms, 5 -> 2000ms
  return 400 + (clampedSpeed - 1) * 450
}

// Константа храящая координаты пути по порядку
export let pathCoordinates: Position[] = []

/**
 * Проверяет, является ли декорация непроходимым препятствием
 */
function isBlockedDecoration(decoration: WorldMapDecorations): boolean {
  switch (decoration) {
    case WorldMapDecorations.MOUNTAINS:
    case WorldMapDecorations.RIVER:
    case WorldMapDecorations.LAKE:
      return true; // Горы, реки, озера - непроходимо
    default:
      return false; // Все остальное проходимо
  }
}

/**
 * Проверяет, можно ли пройти через клетку
 */
function isPassable(worldMap: Record<string, WorldMapCell>, position: Position): boolean {
  const tileKey = `${position.x},${position.y}`
  const tile = worldMap[tileKey]
  
  if (!tile) return true // Если клетки нет в данных, считаем проходимой
  
  // Если клетка заблокирована
  if (tile.blocked) {
   
    return false
  }
  
  // Проверяем декорацию
  if (isBlockedDecoration(tile.decoration)) {
   
    return false
  }
  
  return true
}

/**
 * Получает соседние кл��тки для алгоритма поиска пути
 */
function getNeighbors(position: Position, mapSize: number): Position[] {
  const neighbors: Position[] = []
  const directions = [
    { x: -1, y: 0 },  // Запад
    { x: 1, y: 0 },   // Восток
    { x: 0, y: -1 },  // Север
    { x: 0, y: 1 },   // Юг
    { x: -1, y: -1 }, // Северо-запад
    { x: 1, y: -1 },  // Северо-восток
    { x: -1, y: 1 },  // Юго-запад
    { x: 1, y: 1 }    // Юго-восток
  ]
  
  for (const dir of directions) {
    const newPos = { x: position.x + dir.x, y: position.y + dir.y }
    if (isValidPosition(newPos, mapSize)) {
      neighbors.push(newPos)
    }
  }
  
  return neighbors
}

/**
 * Вычисляет эвристическое расстояние между двумя точками (манхэттенское расстояние)
 */
function heuristic(a: Position, b: Position): number {
  return Math.abs(a.x - b.x) + Math.abs(a.y - b.y)
}

/**
 * Восстанавливает путь из карты предшественников
 */
function reconstructPath(cameFrom: Map<string, Position>, current: Position): Position[] {
  const path: Position[] = []
  let currentPos = current
  
  while (cameFrom.has(`${currentPos.x},${currentPos.y}`)) {
    path.unshift(currentPos)
    currentPos = cameFrom.get(`${currentPos.x},${currentPos.y}`)!
  }
  
  return path
}

/**
 * Функция для вычисления кратчайшего пути до выбранной точки
 * Использует алгоритм A* для поиска пути с учетом препятствий
 */
export function calculateShortestPath(
  mapSize: number,
  playerPosition: Position,
  targetPosition: Position,
  worldMap?: Record<string, WorldMapCell>
): Position[] {
  // Проверяем валидность координат
  if (!isValidPosition(playerPosition, mapSize) || !isValidPosition(targetPosition, mapSize) || window.__playerIsMoving) {
    return []
  }

  // Если игрок уже на целевой позиции
  if (playerPosition.x === targetPosition.x && playerPosition.y === targetPosition.y) {
    return []
  }

  // Если нет данных о карте, используем простой алгоритм
  if (!worldMap) {
    return calculateSimplePath(playerPosition, targetPosition, mapSize)
  }

  // Проверяем, можно ли дойти до целевой позиции
  const targetTile = worldMap[`${targetPosition.x},${targetPosition.y}`]
  if (!isPassable(worldMap, targetPosition)) {
    return []
  }

  // Алгоритм A*
  const openSet = new Set<string>()
  const closedSet = new Set<string>()
  const cameFrom = new Map<string, Position>()
  const gScore = new Map<string, number>()
  const fScore = new Map<string, number>()

  const startKey = `${playerPosition.x},${playerPosition.y}`
  const targetKey = `${targetPosition.x},${targetPosition.y}`

  openSet.add(startKey)
  gScore.set(startKey, 0)
  fScore.set(startKey, heuristic(playerPosition, targetPosition))

  while (openSet.size > 0) {
    // Находим узел с наименьшим fScore
    let current = ''
    let lowestFScore = Infinity
    
    for (const node of openSet) {
      const score = fScore.get(node) || Infinity
      if (score < lowestFScore) {
        lowestFScore = score
        current = node
      }
    }

    if (current === targetKey) {
      // Путь найден!
      const [x, y] = current.split(',').map(Number)
      const path = reconstructPath(cameFrom, { x, y })
      pathCoordinates = path
      return path
    }

    openSet.delete(current)
    closedSet.add(current)

    const [currentX, currentY] = current.split(',').map(Number)
    const currentPos = { x: currentX, y: currentY }
    const neighbors = getNeighbors(currentPos, mapSize)

    for (const neighbor of neighbors) {
      const neighborKey = `${neighbor.x},${neighbor.y}`
      
      if (closedSet.has(neighborKey)) continue
      if (!isPassable(worldMap, neighbor)) continue

      // Все проходимые клетки имеют одинаковую стоимость = 1
      const tentativeGScore = (gScore.get(current) || 0) + 1

      if (!openSet.has(neighborKey)) {
        openSet.add(neighborKey)
      } else if (tentativeGScore >= (gScore.get(neighborKey) || Infinity)) {
        continue
      }

      cameFrom.set(neighborKey, currentPos)
      gScore.set(neighborKey, tentativeGScore)
      fScore.set(neighborKey, tentativeGScore + heuristic(neighbor, targetPosition))
    }
  }

  // Путь не найден
  console.warn('Не удалось найти путь к целевой позиции')
  return []
}

/**
 * Простой алгоритм поиска пути (fallback для случаев без данных о карте)
 */
function calculateSimplePath(
  playerPosition: Position,
  targetPosition: Position,
  mapSize: number
): Position[] {
  const path: Position[] = []
  let current = { ...playerPosition }

  while (current.x !== targetPosition.x || current.y !== targetPosition.y) {
    // Определяем направление движения
    const deltaX = targetPosition.x - current.x
    const deltaY = targetPosition.y - current.y

    // Двигаемся по одной клетке за раз
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Движение по X приоритетнее
      current.x += deltaX > 0 ? 1 : -1
    } else {
      // Движение по Y
      current.y += deltaY > 0 ? 1 : -1
    }

    // Добавляем текущую позицию в путь
    path.push({ ...current })

    // Защита от бесконечного цикла
    if (path.length > mapSize * mapSize) {
      break
    }
  }

  return path
}

/**
 * Проверяет валидность позиции на карте
 */
function isValidPosition(position: Position, mapSize: number): boolean {
  return position.x >= 0 && position.x < mapSize && 
         position.y >= 0 && position.y < mapSize
}

/**
 * Функция для подсветки клеток пути крестиком салатового цвета
 */
export function highlightPathCell(
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  tileWidth: number,
  tileHeight: number
): void {
  const centerX = screenX
  const centerY = screenY
  const crossSize = Math.min(tileWidth, tileHeight) * 0.3

  // Настройки для крестка
  ctx.strokeStyle = '#90EE90' // Салатовый цвет
  ctx.lineWidth = 2
  ctx.lineCap = 'round'

  // Рисуем крестик
  ctx.beginPath()
  
  // Горизонтальная линия
  ctx.moveTo(centerX - crossSize, centerY)
  ctx.lineTo(centerX + crossSize, centerY)
  
  // Вертикальная линия
  ctx.moveTo(centerX, centerY - crossSize/1.5)
  ctx.lineTo(centerX, centerY + crossSize/1.5)
  
  ctx.stroke()
}

/**
 * Получить текущий путь
 */
export function getCurrentPath(): Position[] {
  return [...pathCoordinates]
}

/**
 * Очистить текущий путь
 */
export function clearPath(): void {
  pathCoordinates = []
}

/**
 * Установить новый путь
 */
export function setPath(newPath: Position[]): void {
  pathCoordinates = [...newPath]
}

/**
 * Проверить, является ли позиция частью текущего пути
 */
export function isPositionInPath(position: Position): boolean {
  return pathCoordinates.some(pathPos => 
    pathPos.x === position.x && pathPos.y === position.y
  )
}

/**
 * Открывает туман войны в радиусе от указанной позиции и обновляет позицию игрока
 */
export function revealFogOfWar(
  currentWorld: WorldMap,
  newPlayerPosition: Position,
  radius: number
): WorldMap {
  if (!currentWorld?.worldMap) return currentWorld

  const updatedWorldMap = { ...currentWorld.worldMap }
  
  // Проходим по всем клеткам в радиусе
  for (let dx = -radius; dx <= radius; dx++) {
    for (let dy = -radius; dy <= radius; dy++) {
      const x = newPlayerPosition.x + dx
      const y = newPlayerPosition.y + dy
      
      // Проверяем, что клетка в пределах карты
      const mapSize = currentWorld.settings?.worldSize || 20
      if (x >= 0 && x < mapSize && y >= 0 && y < mapSize) {
        // Проверяем расстояние (круговая область)
        const distance = Math.sqrt(dx * dx + dy * dy)
        if (distance <= radius) {
          const tileKey = `${x},${y}`
          if (updatedWorldMap[tileKey]) {
            updatedWorldMap[tileKey] = {
              ...updatedWorldMap[tileKey],
              fogOfWar: false
            }
          }
        }
      }
    }
  }

  // Обновляем и позицию игрока, и туман войны в одном объекте
  return {
    ...currentWorld,
    worldMap: updatedWorldMap,
    player: currentWorld.player ? {
      ...currentWorld.player,
      position: { ...newPlayerPosition }
    } : undefined
  }
}

/**
 * Перемещает персонажа по массиву пути с задержкой между шагами.
 * Останаливается по нажатию любой клавиши.
 * Напрямую работает с gameStore.
 */
export function movePlayerByPath(
  startPosition: Position,
  path: Position[],
  speed: number = MOVEMENT_SPEED
): void {
  // Применяем множитель для глобальной карты
  const worldMapSpeed = Math.round(speed / WORLD_MAP_SPEED_MULTIPLIER);
  
  let currentIdx = 0
  let currentPos = { ...startPosition }
  let intervalId: NodeJS.Timeout | null = null
  let stopped = false
 window.__playerIsMoving = true;

  function stopMove() {
    if (intervalId) clearInterval(intervalId)
    window.removeEventListener('keydown', stopMove)
    stopped = true
    window.__playerIsMoving = false;
    const { currentWorld, setCurrentWorld, playerLocationPresent } = useGameStore.getState()
    if (currentWorld) {
      let updatedWorld = currentWorld;
      
      // Если игрок в локации, обновляем позицию в локации
      if (playerLocationPresent) {
        import('../location/locationEnterEscapeLogic').then(({ updatePlayerPositionInLocation }) => {
          updatedWorld = updatePlayerPositionInLocation(currentWorld, currentPos);
          setCurrentWorld(updatedWorld);
        });
      } else {
        // Обычное обновление для мира
        if (currentWorld?.player?.parameters?.Perception) {
          const perceptionRadius = Math.max(1, Math.min(10, currentWorld.player.parameters.Perception + 999)) // убрать 999
          updatedWorld = revealFogOfWar(currentWorld, currentPos, perceptionRadius)
        } else {
          updatedWorld = {
            ...currentWorld,
            player: currentWorld?.player ? {
              ...currentWorld.player,
              position: { ...currentPos }
            } : undefined
          }
        }
        setCurrentWorld(updatedWorld)
      }
    }
    clearPath()
  }

  window.addEventListener('keydown', stopMove)

  intervalId = setInterval(() => {
    if (stopped) return
    if (currentIdx >= path.length) {
      stopMove()
      return
    }
    currentPos = { ...path[currentIdx] }
    const { currentWorld, setCurrentWorld, playerLocationPresent } = useGameStore.getState()
    if (currentWorld) {
      let updatedWorld = currentWorld;
      
      // Если игрок в локации, обновляем позицию в локации
      if (playerLocationPresent) {
        import('../location/locationEnterEscapeLogic').then(({ updatePlayerPositionInLocation }) => {
          updatedWorld = updatePlayerPositionInLocation(currentWorld, currentPos);
          setCurrentWorld(updatedWorld);
        });
      } else {
        // Обычное обновление для мира
        if (currentWorld?.player?.parameters?.Perception) {
          const perceptionRadius = Math.max(1, Math.min(10, currentWorld.player.parameters.Perception))
          updatedWorld = revealFogOfWar(currentWorld, currentPos, perceptionRadius)
        } else {
          updatedWorld = {
            ...currentWorld,
            player: currentWorld?.player ? {
              ...currentWorld.player,
              position: { ...currentPos }
            } : undefined
          }
        }
        setCurrentWorld(updatedWorld)
      }
    }
    currentIdx++
    // Если это был последний шаг, сбрасываем флаг движения
    if (currentIdx >= path.length) {
      window.__playerIsMoving = false;
    }
  }, worldMapSpeed)
}
