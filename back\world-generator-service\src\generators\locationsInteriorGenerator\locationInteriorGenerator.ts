import { WorldMapCell } from '../../shared/types/World';
import { locationConfigs, LocationConfig } from './constants/locationConfig';
import { generateGridForLocation } from './generateGridForLocation';
import { placePresetsForLocation } from './placePresetForLocation';
import tokenLegend from './presets/tokenLegend';
import declareDecorationSides, { declareStairsOrientation } from './sideDeclarator';
import { runCleaningPasses } from './cleaningLocation';
import setLocationFloor from './setLocationFloor';
import { generateStreetsForLocation } from './streetsGenerator';
import { ProgressTracker, CancellationToken } from '../../utils/asyncUtils';

// tokenLegend values are numeric constants, allow number|string
const legend = tokenLegend as Record<string, string | number>;

// Основная асинхронная функция генерации контента для всех локаций
export async function generateLocationContentDataAsync(
	worldMap: { worldMapCells: WorldMapCell[] },
	rng: () => number,
	progressTracker?: ProgressTracker,
	cancellationToken?: CancellationToken
): Promise<void> {
	const cells = worldMap.worldMapCells.filter(cell => cell.location);
	const total = cells.length;

	if (total === 0) {
		if (progressTracker) {
			progressTracker.updateStageProgress(100, 'Локации не найдены');
		}
		return;
	}

	// Инициализируем прогресс
	if (progressTracker) {
		progressTracker.updateStageProgress(0, `Обработка ${total} локаций: пресеты, улицы, постобработка`);
	}

	// bounded concurrency worker-pool to avoid fully sequential processing
	let maxConcurrency = 4;
	try {
		const cpus = require('os').cpus?.().length;
		if (cpus && typeof cpus === 'number') maxConcurrency = Math.max(1, Math.floor(cpus / 2));
	} catch (e) {
		// fallback left as default
	}
	const CONCURRENCY = Math.min(Math.max(1, maxConcurrency), total, 8);

	let nextIndex = 0;
	let processed = 0;

	const workers: Promise<void>[] = [];
	for (let w = 0; w < CONCURRENCY; w++) {
		workers.push((async () => {
			while (true) {
				const i = nextIndex++;
				if (i >= total) break;
				if (cancellationToken?.isCancelled) throw new Error('Operation was cancelled');

				const cell = cells[i];
				const location = cell.location!;
				const subtype = location.subtype;
				const config = locationConfigs[subtype];

				await generateLocationInterior(cell, config, rng);

				// update progress (atomic-ish)
				processed++;
				if (progressTracker) {
					const progress = Math.floor((processed / total) * 100);
					progressTracker.updateStageProgress(progress, `Обработка локаций: ${processed}/${total} (пресеты, улицы, постобработка)`);
				}

				// cooperative yield to event loop occasionally (реже, так как randomInterior отключен)
				if (processed % 20 === 0) await new Promise(res => setImmediate(res));
			}
		})());
	}

	await Promise.all(workers);

	// Финальное обновление прогресса
	if (progressTracker) {
		progressTracker.updateStageProgress(100, `Обработка локаций завершена: ${total} локаций`);
	}
}

// Пошаговая генерация интерьера одной локации
async function generateLocationInterior(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number
): Promise<void> {
	const locationStartTime = Date.now();

	// Шаг 1: Генерация грида и базового terrain
	const gridStartTime = Date.now();
	await generateGridForLocation(cell, config, rng);
	const gridTime = Date.now() - gridStartTime;

	// Шаг 2: Генерация улиц (ПЕРЕД buildingsPresetLogic)
	const streetsStartTime = Date.now();
	const streetAreas = await generateStreetsForLocation(cell.location!, config, rng);
	const streetsTime = Date.now() - streetsStartTime;



	// Шаг 3: Размещение пресетов (ОСНОВНАЯ НАГРУЗКА)
	const presetsStartTime = Date.now();
	await placePresetsForLocation(cell, config, rng, legend);
	const presetsTime = Date.now() - presetsStartTime;

	// Шаг 4: Очистка и постобработка
	const postStartTime = Date.now();
	await runCleaningPasses(cell.location!);
	await declareDecorationSides(cell.location!);
	await declareStairsOrientation(cell.location!);
	setLocationFloor(cell.location!);
	const postTime = Date.now() - postStartTime;

	const totalTime = Date.now() - locationStartTime;

	// Логируем время только для первых 3 локаций, чтобы не засорять лог
	if (Math.random() < 0.1) { // 10% шанс логирования
		console.log(`⏱️ Локация ${cell.location?.name}: grid=${gridTime}мс, streets=${streetsTime}мс, presets=${presetsTime}мс, post=${postTime}мс, total=${totalTime}мс`);
	}
}