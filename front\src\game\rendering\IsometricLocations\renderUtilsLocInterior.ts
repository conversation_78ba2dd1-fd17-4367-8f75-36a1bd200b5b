// Для TS: объявляем глобальную функцию для форс-рендера
declare global {
  interface Window {
    __forceLocationRerender?: () => void;
  }
}
/**
 * Утилиты для отрисовки интерьеров локаций в изометрическом стиле
 */

import { WorldMap } from '../../../shared/types/World';
import { TerrainType, LocationDecorations } from '../../../shared/enums';
import { getTileCenterOnScreen, isTileVisible } from '../../utils/coordinates/isometric';

// DECORATION_LOCATION_TEXTURES removed — generate paths conventionally
import { TILE_GAP, DECORATION_TEXTURE_SETTINGS, LOCATION_TERRAIN_TEXTURE_SETTINGS } from '../../utils/constants/renderingLocation';
import { drawSideTextures, isSideDecorationSupported } from './renderSideTextureLocInterior';
import { gameTimeManager } from '../../utils/time/gameTimeManager';
import { isDaytime } from '../../utils/time/gameTime';
import { TERRAIN_LOCATIONS_TEXTURES, TERRAIN_TEXTURES } from '../textures';
import { textureLoader } from '../textures/TextureLoader';

// Небольшая хелпер-функция для детерминированного выбора вариации по координатам
const deterministicVariation = (x: number, y: number, length: number): number => {
  if (!length || length <= 0) return 0;
  const p = (x * Math.PI + y * Math.PI * 2.71828);
  const hash = Math.abs(Math.sin(p) * Math.cos(p * 1.618) * 10000);
  return Math.floor(hash) % length;
};

// day/night handling removed from renderer; managed externally if needed

// Маппинг типов земли локации на текстуры (обновленный под TerrainType)
const LOCATION_TERRAIN_TEXTURES: Record<TerrainType, readonly string[]> = {
  [TerrainType.ASPHALT]: TERRAIN_LOCATIONS_TEXTURES.asphalt, // нет отдельной текстуры, используем grass
  [TerrainType.BETON]: TERRAIN_LOCATIONS_TEXTURES.beton, // нет отдельной текстуры, используем grass
  [TerrainType.WOOD]: TERRAIN_LOCATIONS_TEXTURES.wood, // нет отдельной текстуры, используем grass
  [TerrainType.METAL]: TERRAIN_LOCATIONS_TEXTURES.beton, // нет отдельной текстуры, используем grass
  [TerrainType.GROUND]: TERRAIN_LOCATIONS_TEXTURES.ground,
  [TerrainType.TILES]: TERRAIN_LOCATIONS_TEXTURES.tiles, // наиболее близко к земле
  [TerrainType.WATER]: TERRAIN_TEXTURES.water,
  [TerrainType.WASTELAND]: TERRAIN_LOCATIONS_TEXTURES.wasteland
};

/**
 * Получает детерминированную текстуру для типа земли на основе координат (обновлено под TerrainType)
 */
function getLocationTexture(terrain: TerrainType, x: number, y: number): string | null {
  const textures = LOCATION_TERRAIN_TEXTURES[terrain];
  if (!textures || textures.length === 0) {
    return null;
  }
  // Детерминированный выбор с использованием числа π для лучшего распределения
  const p = (x * Math.PI + y * Math.PI * 2.71828);
  const hash = Math.abs(Math.sin(p) * Math.cos(p * 1.618) * 10000);
  const index = Math.floor(hash) % textures.length;
  return textures[index];
}

/**
 * Менеджер текстур декораций локаций - теперь использует централизованный кэш
 */
class LocationDecorationTextureManager {
  private lastIsDay: boolean | null = null;

  constructor() {
    // Подписываемся на обновления игрового времени, чтобы реагировать на переходы день/ночь
    try {
      const current = gameTimeManager.getCurrentTime();
      this.lastIsDay = isDaytime(current);
      gameTimeManager.addTimeUpdateCallback((t) => {
        try {
          const nowIsDay = isDaytime(t);
          if (this.lastIsDay === null) this.lastIsDay = nowIsDay;
          if (nowIsDay !== this.lastIsDay) {
            // смена day/night — при необходимости можно очистить кэш для дерева
            this.lastIsDay = nowIsDay;
            // Предзагружаем новые текстуры (ненавязчиво)
            this.getDecorationTextures(LocationDecorations.TREE).catch(() => {});
            // Попытка форсировать рендер локации (если доступна глобальная функция)
            try { if (typeof window !== 'undefined' && window.__forceLocationRerender) window.__forceLocationRerender(); } catch (e) {}
          }
        } catch (e) {
          // ignore
        }
      });
    } catch (e) {
      // ignore if gameTimeManager not available
    }
  }

  /**
   * Получает детерминированную вариацию текстуры на основе координат
   */
  private getTextureVariation(decorationType: LocationDecorations, x: number, y: number, texturesLength: number): number {
    // Используем формулу π для детерминированного выбора вариации
    const p = (x * Math.PI * 1.414 + y * Math.PI * 1.732);
    const hash = Math.abs(Math.sin(p * 2.236) * Math.cos(p / 1.618) * 50000);
    return Math.floor(hash) % texturesLength;
  }

  /**
   * Получает текстуры для указанного типа декорации через centralizedLoader
   */
  async getDecorationTextures(decorationType: LocationDecorations): Promise<HTMLImageElement[]> {
    const decorationKey = decorationType as string;
    const basePaths: string[] = [];
    for (let i = 1; i <= 8; i++) basePaths.push(`/textures/Location/decorations/${decorationKey}/${i}.png`);

    // If this decoration has day/night variants, try those subfolders first, then fall back to base paths
  const tryPaths: string[] = [];

    // add base paths afterwards (fallback)
    tryPaths.push(...basePaths);

    // Attempt loading in order; return first successful set (any loaded image is used)
    const images: HTMLImageElement[] = [];
    for (const path of tryPaths) {
      try {
        const img = await textureLoader.loadTexture(path);
        images.push(img);
      } catch (e) {
        // ignore individual load errors — textureLoader will provide placeholder on getTexture
      }
    }

    return images;
  }

  /**
   * Синхронно получает текстуры (если они уже загружены) с детерминированной вариацией
   */
  getLoadedTexturesWithVariation(decorationType: LocationDecorations, x: number, y: number): { textures: HTMLImageElement[], variation: number } | null {
    const decorationKey = decorationType as string;
    const basePaths: string[] = [];
    for (let i = 1; i <= 8; i++) basePaths.push(`/textures/Location/decorations/${decorationKey}/${i}.png`);

  const tryPaths: string[] = [];
    tryPaths.push(...basePaths);

    const loadedTextures = tryPaths
      .map(path => textureLoader.getTexture(path))
      .filter((img): img is HTMLImageElement => img !== undefined);

    if (loadedTextures.length === 0) return null;

    const variation = this.getTextureVariation(decorationType, x, y, loadedTextures.length);
    return { textures: loadedTextures, variation };
  }

  /**
   * Синхронно получает текстуры (если они уже загружены)
   */
  getLoadedTextures(decorationType: LocationDecorations): HTMLImageElement[] | null {
    const decorationKey = decorationType as string;
    const basePaths: string[] = [];
    for (let i = 1; i <= 8; i++) basePaths.push(`/textures/Location/decorations/${decorationKey}/${i}.png`);

  const tryPaths: string[] = [];
    tryPaths.push(...basePaths);

    const loadedTextures = tryPaths
      .map(path => textureLoader.getTexture(path))
      .filter((img): img is HTMLImageElement => img !== undefined);

    return loadedTextures.length > 0 ? loadedTextures : null;
  }

  /**
   * Предзагружает все текстуры декораций
   */
  async preloadAllTextures(): Promise<void> {
  // Предзагрузка отключена: список простых декораций удалён по требованию.
  // Метод оставлен как stub для совместимости и может быть расширен при необходимости.
  return Promise.resolve();
  }
}

// Глобальный экземпляр менеджера текстур декораций локаций
export const locationDecorationTextureManager = new LocationDecorationTextureManager();


const getLocationTerrainColor = (terrain: TerrainType): string => {
  switch (terrain) {
    case TerrainType.ASPHALT:
      return '#555555'; // Серый для асфальта
    case TerrainType.BETON:
      return '#b0b0b0'; // Светло-серый для бетона
    case TerrainType.WOOD:
      return '#deb887'; // Бежевый для дерева
    case TerrainType.METAL:
      return '#a9a9a9'; // Металлический
    case TerrainType.GROUND:
      return '#8B4513'; // Коричневый для земли
    case TerrainType.WATER:
      return '#0000ff'; // Синий для воды
    case TerrainType.WASTELAND:
      return '#8B4513'; // Коричнево-серый для пустоши
    default:
      return '#8B4513'; // По умолчанию коричневый
  }
};

/**
 * Отрисовывает текстуру декорации на ромбовидный тайл с детерминированной вариацией
 */
const drawLocationDecoration = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decorationType: LocationDecorations,
  isoX: number,
  isoY: number,
  rotation: number = 0,
  darknessLevel: number = 0,
  decorationSides?: number[],
  locationData?: any,
  playerPosition: { x: number; y: number } | null = null
): void => {
  // Если декорация VOID — не рисуем ничего
  if (decorationType === LocationDecorations.VOID) return;
  // Обработка декораций с поддержкой decorationSides
  if (isSideDecorationSupported(decorationType)) {
    
    // Если есть decorationSides, используем новую логику
    if (decorationSides && decorationSides.length > 0) {
      drawSideTextures(
        ctx,
        centerX,
        centerY,
        decorationType,
        decorationSides,
        locationData,
        undefined,
        playerPosition,
        { x: isoX, y: isoY }
      );
      return;
    }

    // Старая логика для декораций без decorationSides
    const textures = locationDecorationTextureManager.getLoadedTextures(decorationType);
    if (!textures || textures.length === 0) {
      return; // Текстуры еще не загружены или не найдены
    }

    // Используем детерминированный выбор вариации по координатам, чтобы не вызывать Math.random() каждый кадр
    const variation = deterministicVariation(isoX, isoY, textures.length);
    const texture = textures[variation];
    if (!texture || !texture.complete) {
      // Если текстура ещё не загружена, ничего не делаем — мы не создаём новые объекты в рендер-пути
      return;
    }

    // Отрисовываем декорацию как обычно
    ctx.save();
    
    if (rotation !== 0) {
      ctx.translate(centerX, centerY);
      ctx.rotate(rotation);
      ctx.translate(-centerX, -centerY);
    }

    let drawW = texture.width;
    let drawH = texture.height;

    if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
      const scaleKey = decorationType.toUpperCase();
      const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.getScale(scaleKey);
      
      drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
      drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;
      
      if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
        const aspectRatio = texture.width / texture.height;
        if (aspectRatio > 1) {
          drawH = drawW / aspectRatio;
        } else {
          drawW = drawH * aspectRatio;
        }
      }
    }

    let offsetX = 0;
    let offsetY = 0;
    
    if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
      const decorationKey = decorationType.toUpperCase();
      offsetX = DECORATION_TEXTURE_SETTINGS.getHorizontalOffset(decorationKey);
      offsetY = DECORATION_TEXTURE_SETTINGS.getVerticalOffset(decorationKey);
    }

    // НЕ применяем затемнение к декорации - теперь это делает отдельный слой
    // if (darknessLevel > 0) {
    //   ctx.globalAlpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
    // }

    ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);
    ctx.restore();
    return;
  }

  // Для всех остальных декораций используем детерминированную логику на основе π
  const textureData = locationDecorationTextureManager.getLoadedTexturesWithVariation(decorationType, isoX, isoY);

  if (!textureData || !textureData.textures || textureData.textures.length === 0) {
    // Нет текстуры — рисуем цветную заглушку с восклицательным знаком внутри
    ctx.save();
    // Круг-заглушка
    ctx.fillStyle = '#c313d3ff'; // Серый цвет для отсутствующей текстуры
    ctx.globalAlpha = 0.7;
    ctx.beginPath();
    ctx.arc(centerX, centerY, Math.max(halfTileW, halfTileH) * 0.5, 0, 2 * Math.PI);
    ctx.fill();

    // Восклицательный знак по центру круга
    ctx.globalAlpha = 1;
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    const exclamSize = Math.max(12, Math.floor(Math.max(halfTileW, halfTileH) * 0.6));
    ctx.font = `${exclamSize}px sans-serif`;
    ctx.fillText('!', centerX, centerY);

    ctx.restore();
    return;
  }

  const { textures, variation } = textureData;
  const texture = textures[variation];
  if (!texture || !texture.complete) {
    // Нет текстуры — рисуем цветную заглушку с восклицательным знаком внутри
    ctx.save();
    // Круг-заглушка
    ctx.fillStyle = '#c313d3ff';
    ctx.globalAlpha = 0.7;
    ctx.beginPath();
    ctx.arc(centerX, centerY, Math.max(halfTileW, halfTileH) * 0.5, 0, 2 * Math.PI);
    ctx.fill();

    // Восклицательный знак по центру круга
    ctx.globalAlpha = 1;
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    const exclamSize = Math.max(12, Math.floor(Math.max(halfTileW, halfTileH) * 0.6));
    ctx.font = `${exclamSize}px sans-serif`;
    ctx.fillText('!', centerX, centerY);

    ctx.restore();
    return;
  }

  // Сохраняем состояние контекста
  ctx.save();

  // Применяем поворот если нужно
  if (rotation !== 0) {
    ctx.translate(centerX, centerY);
    ctx.rotate(rotation);
    ctx.translate(-centerX, -centerY);
  }

  // Вычисляем размеры с учетом настроек
  let drawW = texture.width;
  let drawH = texture.height;

  if (DECORATION_TEXTURE_SETTINGS.ENABLE_SCALING) {
    // Получаем множитель масштаба для данного типа декорации
    const scaleKey = decorationType.toUpperCase();
    const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.getScale(scaleKey);
    
    // Применяем базовые размеры и множитель
    drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
    drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;
    
    // Сохраняем пропорции если нужно
    if (DECORATION_TEXTURE_SETTINGS.PRESERVE_ASPECT) {
      const aspectRatio = texture.width / texture.height;
      if (aspectRatio > 1) {
        drawH = drawW / aspectRatio;
      } else {
        drawW = drawH * aspectRatio;
      }
    }
  }

  // Вычисляем смещение позиции если включено
  let offsetX = 0;
  let offsetY = 0;
  
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
      const decorationKey = decorationType.toUpperCase();
      offsetX = DECORATION_TEXTURE_SETTINGS.getHorizontalOffset(decorationKey);
      offsetY = DECORATION_TEXTURE_SETTINGS.getVerticalOffset(decorationKey);
    }

  // НЕ применяем затемнение к декорации - теперь это делает отдельный слой  
  // if (darknessLevel > 0) {
  //   ctx.globalAlpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
  // }

  // Отрисовываем текстуру с вычисленными размерами и смещением
  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);

  // Восстанавливаем состояние контекста
  ctx.restore();
};

/**
 * Отрисовывает объект локации
 */
const drawLocationObject = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  object: any
) => {
  ctx.save();
  
  // Простые символы для объек��ов
  ctx.fillStyle = '#ffff00';
  ctx.font = '10px monospace';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
 
  ctx.restore();
};

/**
 * Отрисовывает ТОЛЬКО декорации для тайла локации (КОПИЯ ИЗ WORLD MAP)
 */
export const drawLocationTileDecorations = (
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  location: any,
  playerPosition: { x: number; y: number } | null,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  darknessLevel: number = 0
) => {
  const { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // Проверяем, находится ли тайл в видимой области
  if (!isTileVisible(centerX, centerY, tileWidth, tileHeight, canvasWidth, canvasHeight)) {
    return;
  }

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла локации
  const locationTileKey = `${isoX},${isoY}`;
  const locationTile = location?.locationMap?.[locationTileKey];

  // Отрисовываем ТОЛЬКО декорации (исключаем VOID — это специальная пустая декорация)
  if (
    locationTile?.decoration &&
    locationTile.decoration !== LocationDecorations.NONE &&
    locationTile.decoration !== LocationDecorations.VOID
  ) {
    drawLocationDecoration(
      ctx,
      centerX,
      centerY,
      halfTileW,
      halfTileH,
      locationTile.decoration,
      isoX,
      isoY,
      0,
      darknessLevel,
      locationTile.decorationSides,
      location,
      playerPosition
    );

   
  }

  // Отрисовываем объекты если есть
  if (locationTile?.objects) {
    drawLocationObject(ctx, centerX, centerY, halfTileW, halfTileH, locationTile.objects);
  }
};

/**
 * Отрисовывает изометрический тайл локации
 */
export const drawLocationTile = (
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  location: any,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  drawPlayer: boolean = true,
  drawDecorations: boolean = true
) => {
  const { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // Проверяем, находится ли тайл в видимой области
  if (!isTileVisible(centerX, centerY, tileWidth, tileHeight, canvasWidth, canvasHeight)) {
    return;
  }

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла локации
  const locationTileKey = `${isoX},${isoY}`;
  const locationTile = location?.locationMap?.[locationTileKey];

  // Рисуем изометрический ромб
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();

  // Определяем текстуру и цвет тайла
  let fillColor = '#8B4513'; // Коричневый по умолчанию для fallback
  let fillOpacity = 1;
  let overlay = false;
  if (locationTile?.spawnZone) {
   
  } else if (locationTile?.goBackZone) {
    fillColor = '#b91515bb'; 
    fillOpacity = 0.8;
    overlay = true;
  } else if (locationTile?.blocked) {
   
  } else if (locationTile?.terrain) {
    fillColor = getLocationTerrainColor(locationTile.terrain);
  }
// скрываем тайл если декорация VOID
if (locationTile?.decoration === LocationDecorations.VOID) {
  fillOpacity = 0;
}
  // Отрисовка тайла:
  let drewTexture = false;
  // Если есть текстура — рисуем её
  if (locationTile?.terrain) {
    const texturePath = getLocationTexture(locationTile.terrain, isoX, isoY);
    if (texturePath) {
      const cachedImg = textureLoader.getTexture(texturePath);
      if (cachedImg && cachedImg.complete && cachedImg.width > 1) {
        ctx.save();
        // Вычисляем угол поворота ТОЛЬКО из настроек констант
        let rotationAngle = 0;
        if (LOCATION_TERRAIN_TEXTURE_SETTINGS.ENABLE_ROTATION && locationTile.terrain) {
          const terrainKey = locationTile.terrain.toUpperCase() as keyof typeof LOCATION_TERRAIN_TEXTURE_SETTINGS.ROTATION_ANGLE;
          const configuredRotation = LOCATION_TERRAIN_TEXTURE_SETTINGS.ROTATION_ANGLE[terrainKey] || 0;
          rotationAngle = configuredRotation;
        }

        const baseTextureSize = 80; // Фиксированный размер для всех тайлов
        let textureWidth = baseTextureSize;
        let textureHeight = baseTextureSize;

        if (LOCATION_TERRAIN_TEXTURE_SETTINGS.ENABLE_SCALING && locationTile.terrain) {
          const terrainKey = locationTile.terrain.toUpperCase() as keyof typeof LOCATION_TERRAIN_TEXTURE_SETTINGS.WIDTH_SCALE;
          const widthScale = LOCATION_TERRAIN_TEXTURE_SETTINGS.WIDTH_SCALE[terrainKey] || 1.0;
          const heightScale = LOCATION_TERRAIN_TEXTURE_SETTINGS.HEIGHT_SCALE[terrainKey] || 1.0;

          textureWidth = baseTextureSize * widthScale;
          textureHeight = baseTextureSize * heightScale;
        }

        // Вычисляем смещение если включено
        let offsetX = 0;
        let offsetY = 0;
        if (LOCATION_TERRAIN_TEXTURE_SETTINGS.ENABLE_OFFSET && locationTile.terrain) {
          const terrainKey = locationTile.terrain.toUpperCase() as keyof typeof LOCATION_TERRAIN_TEXTURE_SETTINGS.HORIZONTAL_OFFSET;
          offsetX = LOCATION_TERRAIN_TEXTURE_SETTINGS.HORIZONTAL_OFFSET[terrainKey] || 0;
          offsetY = LOCATION_TERRAIN_TEXTURE_SETTINGS.VERTICAL_OFFSET[terrainKey] || 0;
        }

        // Переносим систему координат в центр тайла и поворачиваем.
        // Важно: сначала трансформация, затем построение path и clip — тогда область обрезки
        // будет совпадать с трансформированными координатами (работает при повороте).
        ctx.translate(centerX, centerY);
        ctx.rotate(rotationAngle);

        // Обычно обрезаем по ромбу, но для TILES и WOOD нужно показывать полную текстуру
        // (убираем "натягивание" на ромб). Учитываем глобальную настройку CLIP_TO_DIAMOND.
        if (
          LOCATION_TERRAIN_TEXTURE_SETTINGS.CLIP_TO_DIAMOND &&
          locationTile.terrain !== TerrainType.TILES &&
          locationTile.terrain !== TerrainType.WOOD
        ) {
          ctx.beginPath();
          ctx.moveTo(0, -halfTileH);
          ctx.lineTo(halfTileW, 0);
          ctx.lineTo(0, halfTileH);
          ctx.lineTo(-halfTileW, 0);
          ctx.closePath();
          ctx.clip();
        }

        // Применяем итоговую alpha с учётом fillOpacity (например для LocationDecorations.VOID)
        // НЕ применяем darkness к terrain текстурам - это делает отдельный слой затемнения
        let finalAlpha = fillOpacity;
        // Устанавливаем итоговую прозрачность перед отрисовкой текстуры
        ctx.globalAlpha = finalAlpha;

        ctx.drawImage(
          cachedImg,
          -textureWidth / 2 + offsetX,
          -textureHeight / 2 + offsetY,
          textureWidth,
          textureHeight
        );
        ctx.restore();
        drewTexture = true;
      } else {
        // Fire-and-forget загрузка: в случае ошибки логировать не нужно здесь
        void textureLoader.loadTexture(texturePath).catch(() => {});
        fillColor = getLocationTerrainColor(locationTile.terrain);
        drewTexture = false;
      }
    } else {
      fillColor = getLocationTerrainColor(locationTile.terrain);
      drewTexture = false;
    }
  }

  // Если не было текстуры — заливаем цветом
  if (!drewTexture) {
    ctx.save();
    ctx.globalAlpha = fillOpacity;
    ctx.fillStyle = fillColor;
    ctx.fill();
    ctx.restore();
  }

  // Если требуется overlay (spawnZone, goBackZone, blocked) — рисуем поверх текстуры
  if (drewTexture && overlay) {
    ctx.save();
    // Создаем path для ромба заново для overlay
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - halfTileH);
    ctx.lineTo(centerX + halfTileW, centerY);
    ctx.lineTo(centerX, centerY + halfTileH);
    ctx.lineTo(centerX - halfTileW, centerY);
    ctx.closePath();
    
    ctx.globalAlpha = fillOpacity;
    ctx.fillStyle = fillColor;
    ctx.fill();
    ctx.restore();
  }




  // Подсветка выбранного тайла
  if (cellTarget && cellTarget.isoX === isoX && cellTarget.isoY === isoY) {
    ctx.save();
    const insetSize = 4;
    const innerHalfW = halfTileW - insetSize;
    const innerHalfH = halfTileH - insetSize;

    ctx.beginPath();
    ctx.moveTo(centerX, centerY - innerHalfH);
    ctx.lineTo(centerX + innerHalfW, centerY);
    ctx.lineTo(centerX, centerY + innerHalfH);
    ctx.lineTo(centerX - innerHalfW, centerY);
    ctx.closePath();
    ctx.clip();

    ctx.shadowColor = '#00FF00';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    ctx.strokeStyle = '#00FF00';
    ctx.lineWidth = 4;
    ctx.stroke();

    ctx.restore();
  }
};