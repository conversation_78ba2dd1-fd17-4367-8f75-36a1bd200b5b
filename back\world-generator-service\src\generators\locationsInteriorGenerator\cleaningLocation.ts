import { TransferLocation, Point } from '../../shared/types/Location';
import { LocationDecorations, LocationType } from '../../shared/enums';

type CleaningPass = (location: TransferLocation) => Promise<void> | void;

// Registry for cleaning passes so user can add multiple runs
const cleaningPasses: Record<string, CleaningPass> = {};

/**
 * Register a named cleaning pass. You can add custom passes and then run them by name.
 */
export function registerCleaningPass(name: string, pass: CleaningPass) {
  if (!name || typeof pass !== 'function') return;
  cleaningPasses[name] = pass;
}

/**
 * Run registered cleaning passes on the given location.
 * If names array is provided, runs only those passes (in that order).
 * Otherwise runs all registered passes in registration order.
 */
export async function runCleaningPasses(location: TransferLocation, names?: string[]) {
  if (!location) return;

  const toRun = names && names.length
    ? names.map(n => cleaningPasses[n]).filter(Boolean)
    : Object.keys(cleaningPasses).map(k => cleaningPasses[k]);

  for (const p of toRun) {
    try {
      await p(location as TransferLocation);
    } catch (err) {
      // swallow - cleaning passes should be non-fatal
      // in future we can optionally collect diagnostics
    }
  }
}

/**
 * Built-in pass: ensure trees on OUTDOOR locations are not closer than minDistance.
 * Trees that violate spacing are removed and their positions are added to NONE.
 */
export async function enforceOutdoorTreeSpacing(location: TransferLocation, minDistance = 2) {
  if (!location) return;
  if (location.type !== LocationType.OUTDOOR) return;

  location.decorations = location.decorations || {};
  const decos = location.decorations as Partial<Record<string, Point[]>>;

  const treeKey = LocationDecorations.TREE as unknown as string;
  const noneKey = LocationDecorations.NONE as unknown as string;

  const trees = (decos[treeKey] || []).slice();
  if (!trees.length) return;


  function dist(a: Point, b: Point) {
    const dx = a[0] - b[0];
    const dy = a[1] - b[1];
    return Math.sqrt(dx * dx + dy * dy);
  }

  const remaining = trees.slice();
  const removed: Point[] = [];

  function findClosestPair(arr: Point[]) {
    let best = { i: -1, j: -1, d: Infinity };
    for (let i = 0; i < arr.length; i++) {
      for (let j = i + 1; j < arr.length; j++) {
        const d = dist(arr[i], arr[j]);
        if (d < best.d) best = { i, j, d };
      }
    }
    if (best.i === -1) return null;
    return best;
  }

  // Remove closest pairs until the closest distance is >= minDistance
  while (true) {
    const pair = findClosestPair(remaining);
    if (!pair || pair.d >= minDistance) break;

    // Choose which of the pair to remove: prefer to remove the point with
    // more neighbors within minDistance (to reduce future conflicts).
    const neighborCount = (idx: number) =>
      remaining.reduce((c, p, k) => (k !== idx && dist(p, remaining[idx]) < minDistance ? c + 1 : c), 0);

    const c0 = neighborCount(pair.i);
    const c1 = neighborCount(pair.j);
    const removeIndex = c0 > c1 ? pair.i : pair.j;

    const [removedPoint] = remaining.splice(removeIndex, 1);
    removed.push(removedPoint);
  }

  const keep = remaining;

  // write results back
  decos[treeKey] = keep;
  if (!decos[noneKey]) decos[noneKey] = [];
  decos[noneKey] = decos[noneKey].concat(removed);
}

// register the built-in pass under a clear name
// register default pass with a 4-tile minimum spacing
registerCleaningPass('outdoor-tree-spacing', (loc) => enforceOutdoorTreeSpacing(loc, 4));

export default {
  registerCleaningPass,
  runCleaningPasses,
  enforceOutdoorTreeSpacing,
};
