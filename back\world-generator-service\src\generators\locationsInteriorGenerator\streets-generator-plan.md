# План генератора улиц для городов и сел

## Глава 1: Создание файла streetsGenerator.ts
- [ ] Создать новый файл `back/world-generator-service/src/generators/locationsInteriorGenerator/streetsGenerator.ts`
- [ ] Импортировать необходимые типы из существующих файлов
- [ ] Создать основную функцию `generateStreetsForLocation()`
- [ ] Добавить типы для паттернов улиц (T, H, П)
- [ ] Добавить enum для направлений (север-юг, запад-восток)

## Глава 2: Логика выбора паттерна и направления
- [ ] Реализовать RNG для выбора паттерна (T/H/П)
- [ ] Реализовать выбор направления (только одно: север-юг ИЛИ запад-восток)
- [ ] Создать функции для расчета координат улиц от центра карты
- [ ] Добавить валидацию границ карты

## Глава 3: Рисование структуры улиц
- [ ] Реализовать рисование дороги шириной 11 клеток:
  - [ ] Клетки 0-1: TerrainType.TILES (тротуар)
  - [ ] Клетки 2-8: TerrainType.ASPHALT (проезжая часть)
  - [ ] Клетки 9-10: TerrainType.TILES (тротуар)
- [ ] Сохранять 5% существующих декораций (кусты, деревья, бревна, бочки, шины) там где будет дорога
- [ ] Добавить decoration.road с 20% шансом на асфальте

## Глава 4: Размещение объектов на улицах
- [ ] Машины (многоблок 2х5):
  - [ ] 1 штука каждые 20 тайлов, максимум 4 на город
  - [ ] В основном вдоль полос (клетки 3,4 и 7,8)
  - [ ] Иногда поперек дороги (постапокалипсис)
- [ ] Фонари: каждые 10 клеток на тротуаре
- [ ] Скамейки (1х2) + урны: только с севера и запада от асфальта
- [ ] Люки на дороге периодически
- [ ] Знаки иногда
- [ ] Мусор везде понемногу

## Глава 5: Интеграция в основной генератор
- [ ] Добавить вызов `generateStreetsForLocation()` в `locationInteriorGenerator.ts`
- [ ] Вызывать ПЕРЕД `buildingsPresetLogic`
- [ ] Возвращать занятые улицами области
- [ ] Передавать эти области в `buildingsPresetLogic` для избежания пересечений
- [ ] Тестировать на типах локаций: город и село

## Как понял задачу:
- Создать ТОЛЬКО новые функции в новом файле streetsGenerator.ts
- НЕ создавать новые типы, использовать существующие
- НЕ создавать тесты (строго следовать инструкции)
- Улицы: паттерны T/H/П от центра, только одно направление
- Структура: 11 клеток (0-1,9-10 тротуар, 2-8 асфальт)
- Сохранять 5% существующих декораций в области дорог
- Размещать объекты по правилам изометрии

## Что попадает в функцию:
- **location** объект с:
  - `locationSize: [width, height]` - размеры карты
  - `decorations: Record<LocationDecorations, Point[]>` - существующие декорации
  - `subtype` - тип локации (TOWN, VILLAGE)
  - `terrain` - базовый тип земли
- **config** - конфигурация локации
- **rng** - функция рандома
- **legend** - маппинг токенов на декорации

## Что нужно сделать:
- Модифицировать `location.decorations`
- Добавить `location.floor` с TerrainType.TILES и TerrainType.ASPHALT
- Машины = LocationDecorations.CAR (многоблок 2х5)
- Вызов между generateGridForLocation и generateRundimInterior
