
import { LocationSubtype, LocationType, TerrainType } from '../../../shared/enums';

export interface LocationConfig {
	type: LocationType;
	terrain: TerrainType;
	moralityRange: [number, number];
	size: [number, number]; // [min, max] 
	beach?: boolean;
	buildings?: { min: number; max: number };
}

export const locationConfigs: Record<LocationSubtype, LocationConfig> = {

	[LocationSubtype.BUNKER]: {
		type: LocationType.UNDERGROUND,
		terrain: TerrainType.BETON,
		moralityRange: [-1, 20],
		size: [20, 20],
		beach: false,	
		buildings: { min: 4, max: 6 }
	},
  [LocationSubtype.GASSTATION]: {
  type: LocationType.OUTDOOR,
  terrain: TerrainType.WASTELAND,
  moralityRange: [-1, 20],
  size: [70, 80],
  beach: false,
  buildings: { min: 2, max: 2 }
},
  [LocationSubtype.FACTORY]: {
		type: LocationType.INDOOR,
		terrain: TerrainType.BETON,
		moralityRange: [-1, 20],
		size: [20, 20],
		beach: false,
		buildings: { min: 4, max: 6 }
	},
  [LocationSubtype.LABORATORY]: {
		type: LocationType.INDOOR,
		terrain: TerrainType.BETON,
		moralityRange: [-1, 20],
		size: [20, 20],
		beach: false,
		buildings: { min: 4, max: 6 }	
	},
  [LocationSubtype.OTHER]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [70, 80],
		beach: false,
		buildings: { min: 2, max: 6 }
	},
  [LocationSubtype.SHOP]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [70, 80],
		beach: false,
		buildings: { min: 1, max: 2 }
	},
  [LocationSubtype.SCHOOL]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [70, 80],
		beach: false,
		buildings: { min: 1, max: 3 }
	},
  [LocationSubtype.HOSPITAL]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [110, 130],
		beach: false,
		buildings: { min: 1, max: 2 }
	},
  [LocationSubtype.HOTEL]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [70, 80],
		beach: false,
		buildings: { min: 1, max: 2 }
	},
  [LocationSubtype.SUBWAY]: {
		type: LocationType.UNDERGROUND,
		terrain: TerrainType.BETON,
		moralityRange: [-1, 20],
		size: [20, 20],
		beach: false,
		
	},
  [LocationSubtype.POLICE]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [120, 110],
		beach: false,
		buildings: { min: 1, max: 2 }
	},[LocationSubtype.TOWN]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [1, 20],
		size: [200, 200],
		beach: false,
		buildings: { min: 13, max: 15 }
	},
  [LocationSubtype.VILLAGE]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [110, 110],
		beach: false,
		buildings: { min: 5, max: 7 }
	},
  [LocationSubtype.FARM]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [120, 150],
		beach: false,
		buildings: { min: 3, max: 6 }
	},
  [LocationSubtype.MILITARY]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [120, 130],
		beach: false,
		buildings: { min: 1, max: 6 }
	},	[LocationSubtype.CAMP]: {
		type: LocationType.OUTDOOR,
		terrain: TerrainType.WASTELAND,
		moralityRange: [-1, 20],
		size: [70, 80],
		beach: false,
		buildings: { min: 3, max: 4 }
	}

};
