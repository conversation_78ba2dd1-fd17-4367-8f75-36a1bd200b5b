import { PresetLocationMap } from './presetType';
import { subwayPresets } from './core/subway';
import { LocationType, LocationSubtype } from '../../../shared/enums';
import { gasStationPresets } from './core/gasstation';
import { hospitalPresets } from './core/hospital';
import { hotelPresets } from './core/hotel';
import { schoolPresets } from './core/school';
import { policePresets } from './core/police';
import { shopPresets } from './core/shop';
import { militaryPresets } from './core/military';
import { smallHotelPresets } from './buildings/smallHotel';
import { smallHousePresets } from './buildings/smallHouse';
import { fieldPresets } from './buildings/field';


// Маппинг типов локаций к пресетам
export const CORE_PRESET_MAP: Record<LocationType, Record<string, PresetLocationMap[]>> = {
  [LocationType.UNDERGROUND]: {
    [LocationSubtype.SUBWAY]: subwayPresets,
    [LocationSubtype.BUNKER]: subwayPresets,
  },
  [LocationType.INDOOR]: {
    [LocationSubtype.LABORATORY]: subwayPresets,
    [LocationSubtype.FACTORY]: subwayPresets,
  },
  [LocationType.OUTDOOR]: {
    [LocationSubtype.GASSTATION]: gasStationPresets,
    [LocationSubtype.HOSPITAL]: hospitalPresets,
    [LocationSubtype.HOTEL]: hotelPresets,
    [LocationSubtype.SCHOOL]:  schoolPresets,
    [LocationSubtype.POLICE]: policePresets,
    [LocationSubtype.SHOP]: shopPresets,
    [LocationSubtype.MILITARY]: militaryPresets,

    // Заглушка для outdoor
  },
  [LocationType.BEACH]: {
    // Заглушка для beach
  }
};





// Функция для получения пресетов по типу локации и подтипу
export function getPresetsForLocation(locationType: LocationType, subtype: string): PresetLocationMap[] | null {
  const typePresets = CORE_PRESET_MAP[locationType];
  if (!typePresets) return null;

  return typePresets[subtype] || null;
}
