import { LocationConfig } from './constants/locationConfig';
import { LocationDecorations, LocationSubtype, TerrainType } from '../../shared/enums';
import { Point } from '../../shared/types/Location';

// Типы паттернов улиц
enum StreetPattern {
  T = 'T',
  H = 'H',
  U = 'U'  // П-образный паттерн
}

// Направления улиц
enum StreetDirection {
  NORTH_SOUTH = 'north_south',
  EAST_WEST = 'east_west'
}

// Интерфейс для области улицы
interface StreetArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Константы для улиц
const STREET_WIDTH = 11; // Общая ширина улицы
const SIDEWALK_WIDTH = 2; // Ширина тротуара с каждой стороны (клетки 0-1 и 9-10)
const ROAD_START = 2; // Начало проезжей части
const ROAD_END = 8; // Конец проезжей части
const CAR_SPAWN_INTERVAL = 12; // Более частая попытка спавна машин (ранее 20)
const LIGHT_INTERVAL = 10; // Интервал фонарей (оставляем, но теперь только север/запад)
const DECORATION_PRESERVE_CHANCE = 0.05; // 5% шанс сохранить декорации в области дорог

// Параметры градиента появления объектов от края карты
// Новая логика края:
// Первые 15 клеток от любого края: дороги (покрытие) не рисуются вообще.
// Зона 15..30: градиент появления объектов: на 15 => 10%, на 30 => 100%.
// После 30: полный шанс (1.0)
const STREET_NO_ROAD_MARGIN = 15; // физически не рисуем улицу
const EDGE_GRADIENT_START = 15;   // начало градиента спавна объектов
const EDGE_GRADIENT_END = 30;     // конец градиента (100%)

// Базовые шансы спавна (будут умножаться на edge multiplier)
const ROAD_DECORATION_BASE_CHANCE = 0.2;
const CAR_BASE_CHANCE = 0.35; // немного увеличено
const CAR_CROSSED_BASE_CHANCE = 0.2;
const LIGHT_BASE_CHANCE = 0.8; // применяется только к одной стороне
const BENCH_PAIR_BASE_CHANCE = 0.5; // шанс сгенерировать пару лавок
const TRASHBIN_AFTER_BENCH_CHANCE = 0.8; // шанс урны рядом с лавкой
const MANHOLE_BASE_CHANCE = 0.15;
const SIGN_BASE_CHANCE = 0.1;
const LITTER_BASE_CHANCE = 0.02;

/**
 * Возвращает лимит машин для данной локации (рандом внутри диапазона).
 * VILLAGE: 2-4, TOWN: 5-7
 */
function getCarLimit(subtype: LocationSubtype, rng: () => number): number {
  if (subtype === LocationSubtype.VILLAGE) {
    return 2 + Math.floor(rng() * 3); // 2..4
  }
  if (subtype === LocationSubtype.TOWN) {
    return 5 + Math.floor(rng() * 3); // 5..7
  }
  return 3; // fallback
}

/**
 * Множитель появления объектов в зависимости от расстояния до края карты.
 * 0 при расстоянии <= EDGE_ZERO_DISTANCE, 1 при >= EDGE_FULL_DISTANCE, линейно между.
 */
function edgeSpawnMultiplier(x: number, y: number, mapWidth: number, mapHeight: number): number {
  const distLeft = x;
  const distTop = y;
  const distRight = mapWidth - 1 - x;
  const distBottom = mapHeight - 1 - y;
  const minDist = Math.min(distLeft, distTop, distRight, distBottom);
  if (minDist < EDGE_GRADIENT_START) return 0; // 0% до 15
  if (minDist >= EDGE_GRADIENT_END) return 1;  // 100% после 30
  // Линейная интерполяция от 10% до 100% между 15 и 30
  const t = (minDist - EDGE_GRADIENT_START) / (EDGE_GRADIENT_END - EDGE_GRADIENT_START);
  return 0.1 + 0.9 * t;
}

/**
 * Главная функция генерации улиц для локации
 */
export async function generateStreetsForLocation(
  location: any,
  config: LocationConfig,
  rng: () => number
): Promise<StreetArea[]> {
  // Проверяем, нужны ли улицы для данного типа локации
  if (!shouldGenerateStreets(location.subtype)) {
    return [];
  }

  const [width, height] = location.locationSize;
  const centerX = Math.floor(width / 2);
  const centerY = Math.floor(height / 2);

  // Выбираем паттерн и направление
  const pattern = selectStreetPattern(rng);
  const direction = selectStreetDirection(rng);

  console.log(`Generating streets: pattern=${pattern}, direction=${direction}, center=(${centerX}, ${centerY})`);

  // Проверяем валидность паттерна и направления
  if (!pattern || !direction) {
    console.error('Invalid pattern or direction:', { pattern, direction });
    return [];
  }

  // Генерируем улицы (с упорядочиванием: сначала primary по выбранному direction, затем вторичные)
  let streetAreas = generateStreetAreas(pattern, direction, centerX, centerY, width, height, rng);

  // Фильтруем undefined значения
  streetAreas = streetAreas.filter(area => area && typeof area.width === 'number' && typeof area.height === 'number');

  // Оптимизация: для Village оставляем только одну (центральную) улицу
  if (location.subtype === LocationSubtype.VILLAGE) {
    // Выбираем одну улицу: предпочтительно совпадающую с direction и ближе всего к центру
    streetAreas = streetAreas.sort((a,b)=>{
      const aIsDir = (direction === StreetDirection.EAST_WEST ? a.width > a.height : a.height > a.width);
      const bIsDir = (direction === StreetDirection.EAST_WEST ? b.width > b.height : b.height > b.width);
      if (aIsDir !== bIsDir) return aIsDir ? -1 : 1;
      const acx = a.x + a.width/2, acy = a.y + a.height/2;
      const bcx = b.x + b.width/2, bcy = b.y + b.height/2;
      const da = Math.abs(acx - centerX) + Math.abs(acy - centerY);
      const db = Math.abs(bcx - centerX) + Math.abs(bcy - centerY);
      return da - db;
    }).slice(0,1);
  } else {
    // Town: полная логика с ordering и offset
    streetAreas = applyPrimarySecondaryOrdering(streetAreas, direction);
    streetAreas = applySecondaryOffset(streetAreas, direction, width, height);
  }

  // Рисуем улицы
  await drawStreets(location, streetAreas, rng);

  // Определяем лимит машин для этой локации
  const carLimit = getCarLimit(location.subtype, rng);

  // Размещаем объекты на улицах (включая распределение машин по всем улицам)
  await placeStreetObjects(location, streetAreas, rng, carLimit);
  // Перекрестки теперь не требуют постобработки: правило перекрытия применено при рисовании.

  // Сохраняем области улиц в локации для последующего размещения зданий (ROAD паттерн)
  try {
    (location as any).streetAreas = streetAreas.map(a => ({ x: a.x, y: a.y, width: a.width, height: a.height }));
  } catch {}

  return streetAreas;
}

/**
 * Проверяет, нужно ли генерировать улицы для данного подтипа локации
 */
function shouldGenerateStreets(subtype: LocationSubtype): boolean {
  return subtype === LocationSubtype.TOWN || subtype === LocationSubtype.VILLAGE;
}

/**
 * Выбирает случайный паттерн улиц
 */
function selectStreetPattern(rng: () => number): StreetPattern {
  const patterns = [StreetPattern.T, StreetPattern.H, StreetPattern.U];
  const index = Math.floor(rng() * patterns.length);
  return patterns[index];
}

/**
 * Выбирает направление улиц (только одно направление)
 */
function selectStreetDirection(rng: () => number): StreetDirection {
  const directions = [StreetDirection.NORTH_SOUTH, StreetDirection.EAST_WEST];
  const index = Math.floor(rng() * directions.length);
  return directions[index];
}

/**
 * Генерирует области улиц в зависимости от паттерна
 */
function generateStreetAreas(
  pattern: StreetPattern,
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number,
  rng: () => number
): StreetArea[] {
  const areas: StreetArea[] = [];

  // Проверяем валидность входных параметров
  if (!pattern || !direction || width <= 0 || height <= 0) {
    console.error('Invalid parameters for generateStreetAreas:', { pattern, direction, width, height });
    return [];
  }

  try {
    switch (pattern) {
      case StreetPattern.T:
        areas.push(...generateTPattern(direction, centerX, centerY, width, height));
        break;
      case StreetPattern.H:
        areas.push(...generateHPattern(direction, centerX, centerY, width, height));
        break;
      case StreetPattern.U:
        areas.push(...generateUPattern(direction, centerX, centerY, width, height));
        break;
      default:
        console.error('Unknown street pattern:', pattern);
        return [];
    }
  } catch (error) {
    console.error('Error generating street areas:', error);
    return [];
  }

  return areas.filter(area => area && isValidStreetArea(area, width, height));
}

/**
 * Генерирует T-образный паттерн
 */
function generateTPattern(
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number
): StreetArea[] {
  const areas: StreetArea[] = [];

  if (direction === StreetDirection.NORTH_SOUTH) {
    // Вертикальная линия через центр
    areas.push({
      x: centerX - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    // Горизонтальная линия в верхней части
    areas.push({
      x: 0,
      y: Math.floor(height / 3) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
  } else {
    // Горизонтальная линия через центр
    areas.push({
      x: 0,
      y: centerY - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    // Вертикальная линия в левой части
    areas.push({
      x: Math.floor(width / 3) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
  }

  return areas;
}

/**
 * Генерирует H-образный паттерн
 */
function generateHPattern(
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number
): StreetArea[] {
  const areas: StreetArea[] = [];

  if (direction === StreetDirection.NORTH_SOUTH) {
    // Две вертикальные линии
    areas.push({
      x: Math.floor(width / 3) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    areas.push({
      x: Math.floor(2 * width / 3) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    // Горизонтальная соединяющая линия
    areas.push({
      x: 0,
      y: centerY - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
  } else {
    // Две горизонтальные линии
    areas.push({
      x: 0,
      y: Math.floor(height / 3) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    areas.push({
      x: 0,
      y: Math.floor(2 * height / 3) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    // Вертикальная соединяющая линия
    areas.push({
      x: centerX - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
  }

  return areas;
}

/**
 * Генерирует П-образный паттерн
 */
function generateUPattern(
  direction: StreetDirection,
  centerX: number,
  centerY: number,
  width: number,
  height: number
): StreetArea[] {
  const areas: StreetArea[] = [];

  if (direction === StreetDirection.NORTH_SOUTH) {
    // Две вертикальные линии по краям
    areas.push({
      x: Math.floor(width / 4) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    areas.push({
      x: Math.floor(3 * width / 4) - Math.floor(STREET_WIDTH / 2),
      y: 0,
      width: STREET_WIDTH,
      height: height
    });
    // Горизонтальная соединяющая линия внизу
    areas.push({
      x: Math.floor(width / 4),
      y: Math.floor(3 * height / 4) - Math.floor(STREET_WIDTH / 2),
      width: Math.floor(width / 2),
      height: STREET_WIDTH
    });
  } else {
    // Две горизонтальные линии по краям
    areas.push({
      x: 0,
      y: Math.floor(height / 4) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    areas.push({
      x: 0,
      y: Math.floor(3 * height / 4) - Math.floor(STREET_WIDTH / 2),
      width: width,
      height: STREET_WIDTH
    });
    // Вертикальная соединяющая линия справа
    areas.push({
      x: Math.floor(3 * width / 4) - Math.floor(STREET_WIDTH / 2),
      y: Math.floor(height / 4),
      width: STREET_WIDTH,
      height: Math.floor(height / 2)
    });
  }

  return areas;
}

/**
 * Проверяет валидность области улицы
 */
function isValidStreetArea(area: StreetArea, mapWidth: number, mapHeight: number): boolean {
  if (!area || typeof area.x !== 'number' || typeof area.y !== 'number' || 
      typeof area.width !== 'number' || typeof area.height !== 'number') {
    return false;
  }
  
  return area.x >= 0 && area.y >= 0 &&
         area.x + area.width <= mapWidth &&
         area.y + area.height <= mapHeight &&
         area.width > 0 && area.height > 0;
}

/**
 * Рисует улицы на локации
 */
async function drawStreets(location: any, streetAreas: StreetArea[], rng: () => number): Promise<void> {
  // Инициализируем floor если его нет
  if (!location.floor) {
    location.floor = {};
  }
  if (!location.floor[TerrainType.TILES]) {
    location.floor[TerrainType.TILES] = [];
  }
  if (!location.floor[TerrainType.ASPHALT]) {
    location.floor[TerrainType.ASPHALT] = [];
  }

  // Используем множества для быстрого перекрытия
  const key = (x:number,y:number)=> x+":"+y;
  const tilesSet = new Set<string>(location.floor[TerrainType.TILES].map(([x,y]:[number,number])=>key(x,y)));
  const asphaltSet = new Set<string>(location.floor[TerrainType.ASPHALT].map(([x,y]:[number,number])=>key(x,y)));

  for (const area of streetAreas) {
    await drawSingleStreet(location, area, rng, tilesSet, asphaltSet);
  }

  // Пересобираем массивы
  location.floor[TerrainType.TILES] = Array.from(tilesSet).map(s=>{const [xs,ys]=s.split(':').map(Number);return [xs,ys];});
  location.floor[TerrainType.ASPHALT] = Array.from(asphaltSet).map(s=>{const [xs,ys]=s.split(':').map(Number);return [xs,ys];});
}

/**
 * Рисует одну улицу
 */
async function drawSingleStreet(location: any, area: StreetArea, rng: () => number, tilesSet: Set<string>, asphaltSet: Set<string>): Promise<void> {
  const [mapWidth, mapHeight] = location.locationSize;
  for (let x = area.x; x < area.x + area.width; x++) {
    for (let y = area.y; y < area.y + area.height; y++) {
      // Пропускаем тайлы улицы в зоне полного запрета (до 15 клеток от края)
      const distLeft = x;
      const distTop = y;
      const distRight = mapWidth - 1 - x;
      const distBottom = mapHeight - 1 - y;
      const minDist = Math.min(distLeft, distTop, distRight, distBottom);
      if (minDist < STREET_NO_ROAD_MARGIN) continue;
      const relativeX = x - area.x;
      const relativeY = y - area.y;

      // Определяем тип покрытия в зависимости от позиции в улице
      const terrainType = getStreetTerrainType(relativeX, relativeY, area);

      // Edge multiplier для покрытия: используем тот же градиент, но интерпретация: 
      // в зоне 15..30 шанс присутствия покрытия = edgeMult (минимум 0.1 там)
      const coverChance = edgeSpawnMultiplier(x, y, mapWidth, mapHeight);
      if (coverChance > 0 && rng() < coverChance) {
        const k = x+":"+y;
        if (terrainType === TerrainType.ASPHALT) {
          // Асфальт вытесняет плитку
            if (tilesSet.has(k)) tilesSet.delete(k);
            asphaltSet.add(k);
            // При появлении асфальта удаляем блокирующие уличные декорации (лавка, фонарь, урна)
            removeBlockingStreetDecorations(location, x, y);
        } else {
          // Плитка НЕ вытесняет асфальт
          if (!asphaltSet.has(k)) tilesSet.add(k);
        }
      } else {
        // если не проходит шанс покрытия — пропускаем; это создаёт «рваный» край
        continue;
      }

      // Очищаем существующие декорации с шансом сохранения 5%
      await clearExistingDecorations(location, x, y, rng);

      // Edge multiplier
      const edgeMult = edgeSpawnMultiplier(x, y, mapWidth, mapHeight);

      // Добавляем decoration.road с базовым шансом * edgeMult на асфальте
      if (terrainType === TerrainType.ASPHALT && edgeMult > 0 && rng() < ROAD_DECORATION_BASE_CHANCE * edgeMult) {
        if (!location.decorations[LocationDecorations.ROAD]) {
          location.decorations[LocationDecorations.ROAD] = [];
        }
        location.decorations[LocationDecorations.ROAD].push([x, y]);
      }
    }
  }
}
// Удалена постобработка refineIntersections как ненужная с новым правилом перекрытия.
// Вспомогательный: одиночный кандидат (новый распределенный способ)
async function placeCarAtCandidate(location: any, area: StreetArea, baseX: number, baseY: number, rng: () => number): Promise<number> {
  const isHorizontal = area.width > area.height;
  const [mapWidth, mapHeight] = location.locationSize;
  if (!location.decorations[LocationDecorations.CAR]) location.decorations[LocationDecorations.CAR] = [];
  const lanes = [3, 4, 7, 8];
  if (isHorizontal) {
    const x = baseX;
    const lane = lanes[Math.floor(rng() * lanes.length)];
    const carY = area.y + lane;
    const edgeMult = edgeSpawnMultiplier(x, carY, mapWidth, mapHeight);
    if (edgeMult === 0) return 0;
    const width = 2, height = 5;
    const crossed = rng() < CAR_CROSSED_BASE_CHANCE;
    const chance = CAR_BASE_CHANCE * edgeMult;
    if (rng() < chance) {
      if (crossed) {
        if (canPlaceCar(location, x, carY - 2, height, width)) {
          addCarMultiBlock(location, x, carY - 2, height, width);
          return 1;
        }
      } else {
        if (canPlaceCar(location, x, carY, width, height)) {
          addCarMultiBlock(location, x, carY, width, height);
          return 1;
        }
      }
    }
  } else {
    const y = baseY;
    const lane = lanes[Math.floor(rng() * lanes.length)];
    const carX = area.x + lane;
    const edgeMult = edgeSpawnMultiplier(carX, y, mapWidth, mapHeight);
    if (edgeMult === 0) return 0;
    const width = 2, height = 5;
    const crossed = rng() < CAR_CROSSED_BASE_CHANCE;
    const chance = CAR_BASE_CHANCE * edgeMult;
    if (rng() < chance) {
      if (crossed) {
        if (canPlaceCar(location, carX - 2, y, height, width)) {
          addCarMultiBlock(location, carX - 2, y, height, width);
          return 1;
        }
      } else {
        if (canPlaceCar(location, carX, y, width, height)) {
          addCarMultiBlock(location, carX, y, width, height);
          return 1;
        }
      }
    }
  }
  return 0;
}

// Добавляет все клетки многоблочного автомобиля
function addCarMultiBlock(location: any, x: number, y: number, width: number, height: number) {
  for (let dx = 0; dx < width; dx++) {
    for (let dy = 0; dy < height; dy++) {
      location.decorations[LocationDecorations.CAR].push([x + dx, y + dy]);
    }
  }
}

/**
 * Проверяет, можно ли разместить машину в данной позиции
 */
function canPlaceCar(location: any, x: number, y: number, width: number, height: number): boolean {
  const [mapWidth, mapHeight] = location.locationSize;

  // Проверяем границы карты
  if (x < 0 || y < 0 || x + width > mapWidth || y + height > mapHeight) {
    return false;
  }

  // Проверяем, нет ли уже машины в этой области
  const cars = location.decorations[LocationDecorations.CAR] || [];
  for (const [carX, carY] of cars) {
    if (x < carX + 2 && x + width > carX && y < carY + 5 && y + height > carY) {
      return false; // Пересечение с существующей машиной
    }
  }

  return true;
}

/**
 * Размещает фонари на тротуарах
 */
async function placeLights(location: any, area: StreetArea, rng: () => number, asphaltSet?: Set<string>): Promise<void> {
  if (!location.decorations[LocationDecorations.EXTERIORLIGHT]) {
    location.decorations[LocationDecorations.EXTERIORLIGHT] = [];
  }
  const isHorizontal = area.width > area.height;
  const [mapWidth, mapHeight] = location.locationSize;
  if (isHorizontal) {
    // Только северный тротуар (y+1)
    for (let x = area.x; x < area.x + area.width; x += LIGHT_INTERVAL) {
      const edgeMult = edgeSpawnMultiplier(x, area.y + 1, mapWidth, mapHeight);
      if (edgeMult > 0 && rng() < LIGHT_BASE_CHANCE * edgeMult) {
        const k = x + ":" + (area.y + 1);
        if (!asphaltSet || !asphaltSet.has(k)) { // запрет спавна на асфальте
          location.decorations[LocationDecorations.EXTERIORLIGHT].push([x, area.y + 1]);
        }
      }
    }
  } else {
    // Только западный тротуар (x+1)
    for (let y = area.y; y < area.y + area.height; y += LIGHT_INTERVAL) {
      const edgeMult = edgeSpawnMultiplier(area.x + 1, y, mapWidth, mapHeight);
      if (edgeMult > 0 && rng() < LIGHT_BASE_CHANCE * edgeMult) {
        const k = (area.x + 1) + ":" + y;
        if (!asphaltSet || !asphaltSet.has(k)) {
          location.decorations[LocationDecorations.EXTERIORLIGHT].push([area.x + 1, y]);
        }
      }
    }
  }
}

/**
 * Размещает скамейки и урны (только с севера и запада от асфальта для изометрии)
 */
async function placeBenchesAndTrashBins(location: any, area: StreetArea, rng: () => number, asphaltSet?: Set<string>): Promise<void> {
  if (!location.decorations[LocationDecorations.BENCH]) {
    location.decorations[LocationDecorations.BENCH] = [];
  }
  if (!location.decorations[LocationDecorations.TRASHBIN]) {
    location.decorations[LocationDecorations.TRASHBIN] = [];
  }
  const isHorizontal = area.width > area.height;
  const [mapWidth, mapHeight] = location.locationSize;
  if (isHorizontal) {
    // Только северный тротуар, пары лавок
    const benchY = area.y + 1;
    for (let x = area.x; x < area.x + area.width; x += 12) {
      const edgeMult = edgeSpawnMultiplier(x, benchY, mapWidth, mapHeight);
      if (edgeMult === 0) continue;
      if (rng() < BENCH_PAIR_BASE_CHANCE * edgeMult) {
        // Пара лавок вплотную (x и x+1)
        const k1 = x + ":" + benchY;
        const k2 = (x + 1) + ":" + benchY;
        if ((!asphaltSet || !asphaltSet.has(k1)) && canPlaceBench(location, x, benchY)) location.decorations[LocationDecorations.BENCH].push([x, benchY]);
        if ((!asphaltSet || !asphaltSet.has(k2)) && canPlaceBench(location, x + 1, benchY)) location.decorations[LocationDecorations.BENCH].push([x + 1, benchY]);
        // Урны (между или после)
        if (rng() < TRASHBIN_AFTER_BENCH_CHANCE) {
          const kt1 = (x - 1) + ":" + benchY;
          if (!asphaltSet || !asphaltSet.has(kt1)) location.decorations[LocationDecorations.TRASHBIN].push([x - 1, benchY]);
        }
        if (rng() < TRASHBIN_AFTER_BENCH_CHANCE * 0.6) {
          const kt2 = (x + 2) + ":" + benchY;
          if (!asphaltSet || !asphaltSet.has(kt2)) location.decorations[LocationDecorations.TRASHBIN].push([x + 2, benchY]);
        }
      }
    }
  } else {
    // Только западный тротуар, пары лавок
    const benchX = area.x + 1;
    for (let y = area.y; y < area.y + area.height; y += 12) {
      const edgeMult = edgeSpawnMultiplier(benchX, y, mapWidth, mapHeight);
      if (edgeMult === 0) continue;
      if (rng() < BENCH_PAIR_BASE_CHANCE * edgeMult) {
        const k1 = benchX + ":" + y;
        const k2 = benchX + ":" + (y + 1);
        if ((!asphaltSet || !asphaltSet.has(k1)) && canPlaceBench(location, benchX, y)) location.decorations[LocationDecorations.BENCH].push([benchX, y]);
        if ((!asphaltSet || !asphaltSet.has(k2)) && canPlaceBench(location, benchX, y + 1)) location.decorations[LocationDecorations.BENCH].push([benchX, y + 1]);
        if (rng() < TRASHBIN_AFTER_BENCH_CHANCE) {
          const kt1 = benchX + ":" + (y - 1);
            if (!asphaltSet || !asphaltSet.has(kt1)) location.decorations[LocationDecorations.TRASHBIN].push([benchX, y - 1]);
        }
        if (rng() < TRASHBIN_AFTER_BENCH_CHANCE * 0.6) {
          const kt2 = benchX + ":" + (y + 2);
          if (!asphaltSet || !asphaltSet.has(kt2)) location.decorations[LocationDecorations.TRASHBIN].push([benchX, y + 2]);
        }
      }
    }
  }
}

/**
 * Проверяет, можно ли разместить скамейку (многоблок 1х2)
 */
function canPlaceBench(location: any, x: number, y: number): boolean {
  const [mapWidth, mapHeight] = location.locationSize;

  // Проверяем границы карты для скамейки 1х2
  if (x < 0 || y < 0 || x + 1 > mapWidth || y + 2 > mapHeight) {
    return false;
  }

  return true;
}

/**
 * Размещает люки на дороге
 */
async function placeManholeCovers(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.MANHOLECOVER]) {
    location.decorations[LocationDecorations.MANHOLECOVER] = [];
  }
  const isHorizontal = area.width > area.height;
  const [mapWidth, mapHeight] = location.locationSize;
  if (isHorizontal) {
    for (let x = area.x; x < area.x + area.width; x += 25) {
      const manholeY = area.y + ROAD_START + Math.floor(rng() * (ROAD_END - ROAD_START + 1));
      const edgeMult = edgeSpawnMultiplier(x, manholeY, mapWidth, mapHeight);
      if (edgeMult > 0 && rng() < MANHOLE_BASE_CHANCE * edgeMult) {
        location.decorations[LocationDecorations.MANHOLECOVER].push([x, manholeY]);
      }
    }
  } else {
    for (let y = area.y; y < area.y + area.height; y += 25) {
      const manholeX = area.x + ROAD_START + Math.floor(rng() * (ROAD_END - ROAD_START + 1));
      const edgeMult = edgeSpawnMultiplier(manholeX, y, mapWidth, mapHeight);
      if (edgeMult > 0 && rng() < MANHOLE_BASE_CHANCE * edgeMult) {
        location.decorations[LocationDecorations.MANHOLECOVER].push([manholeX, y]);
      }
    }
  }
}

/**
 * Размещает знаки
 */
async function placeSigns(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.SIGN]) {
    location.decorations[LocationDecorations.SIGN] = [];
  }
  const isHorizontal = area.width > area.height;
  const [mapWidth, mapHeight] = location.locationSize;
  if (isHorizontal) {
    for (let x = area.x; x < area.x + area.width; x += 25) { // немного чаще
      const northY = area.y + 1;
      const southY = area.y + area.height - 2;
      const northMult = edgeSpawnMultiplier(x, northY, mapWidth, mapHeight);
      const southMult = edgeSpawnMultiplier(x, southY, mapWidth, mapHeight);
      // Выбираем сторону с учетом edge multiplier (север предпочтителен для стиля)
      if (northMult > 0 && rng() < SIGN_BASE_CHANCE * northMult) {
        location.decorations[LocationDecorations.SIGN].push([x, northY]);
      } else if (southMult > 0 && rng() < (SIGN_BASE_CHANCE * 0.6) * southMult) {
        // Иногда южная сторона реже
        location.decorations[LocationDecorations.SIGN].push([x, southY]);
      }
    }
  } else {
    for (let y = area.y; y < area.y + area.height; y += 25) { // немного чаще
      const westX = area.x + 1;
      const eastX = area.x + area.width - 2;
      const westMult = edgeSpawnMultiplier(westX, y, mapWidth, mapHeight);
      const eastMult = edgeSpawnMultiplier(eastX, y, mapWidth, mapHeight);
      if (westMult > 0 && rng() < SIGN_BASE_CHANCE * westMult) {
        location.decorations[LocationDecorations.SIGN].push([westX, y]);
      } else if (eastMult > 0 && rng() < (SIGN_BASE_CHANCE * 0.6) * eastMult) {
        location.decorations[LocationDecorations.SIGN].push([eastX, y]);
      }
    }
  }
}

/**
 * Размещает мусор везде понемногу
 */
async function placeLitter(location: any, area: StreetArea, rng: () => number): Promise<void> {
  if (!location.decorations[LocationDecorations.LITTER]) {
    location.decorations[LocationDecorations.LITTER] = [];
  }
  const [mapWidth, mapHeight] = location.locationSize;
  for (let x = area.x; x < area.x + area.width; x++) {
    for (let y = area.y; y < area.y + area.height; y++) {
      const edgeMult = edgeSpawnMultiplier(x, y, mapWidth, mapHeight);
      if (edgeMult > 0 && rng() < LITTER_BASE_CHANCE * edgeMult) {
        location.decorations[LocationDecorations.LITTER].push([x, y]);
      }
    }
  }
}


// === Primary / Secondary helpers ===
function applyPrimarySecondaryOrdering(areas: StreetArea[], direction: StreetDirection): StreetArea[] {
  // Primary — те, что совпадают с direction (горизонтальные если EAST_WEST, вертикальные если NORTH_SOUTH)
  const primary = [] as StreetArea[];
  const secondary = [] as StreetArea[];
  for (const a of areas) {
    const isHorizontal = a.width > a.height;
    if ((direction === StreetDirection.EAST_WEST && isHorizontal) || (direction === StreetDirection.NORTH_SOUTH && !isHorizontal)) {
      primary.push(a);
    } else {
      secondary.push(a);
    }
  }
  return [...primary, ...secondary];
}

function applySecondaryOffset(areas: StreetArea[], direction: StreetDirection, mapWidth: number, mapHeight: number): StreetArea[] {
  const result: StreetArea[] = [];
  for (const a of areas) {
    const isHorizontal = a.width > a.height;
    let modified = { ...a };
    // Сдвигаем только вторичные
    const isPrimary = (direction === StreetDirection.EAST_WEST && isHorizontal) || (direction === StreetDirection.NORTH_SOUTH && !isHorizontal);
    if (!isPrimary) {
      if (isHorizontal) {
        // горизонтальная вторичная — сдвинуть x на 3 если позволяет карта
        modified.x = Math.min(modified.x + 3, mapWidth - modified.width);
      } else {
        // вертикальная вторичная — сдвинуть y на 3
        modified.y = Math.min(modified.y + 3, mapHeight - modified.height);
      }
    }
    result.push(modified);
  }
  return result;
}

// === ВОССТАНОВЛЕННЫЕ ХЕЛПЕРЫ (для компактности помещены в конец) ===

function getStreetTerrainType(relativeX: number, relativeY: number, area: StreetArea): TerrainType {
  if (area.width > area.height) {
    if (relativeY <= 1 || relativeY >= STREET_WIDTH - 2) return TerrainType.TILES;
    if (relativeY >= ROAD_START && relativeY <= ROAD_END) return TerrainType.ASPHALT;
  } else {
    if (relativeX <= 1 || relativeX >= STREET_WIDTH - 2) return TerrainType.TILES;
    if (relativeX >= ROAD_START && relativeX <= ROAD_END) return TerrainType.ASPHALT;
  }
  return TerrainType.TILES;
}

async function clearExistingDecorations(location: any, x: number, y: number, rng: () => number): Promise<void> {
  if (!location.decorations) return;
  const clearable = [
    LocationDecorations.GRASS,
    LocationDecorations.BUSH,
    LocationDecorations.TREE,
    LocationDecorations.LOG,
    LocationDecorations.ROCKS,
    LocationDecorations.TIRE,
    LocationDecorations.BARREL,
    LocationDecorations.MUD
  ];
  for (const type of clearable) {
    const arr = location.decorations[type];
    if (!Array.isArray(arr)) continue;
    const idx = arr.findIndex(([dx,dy]:Point)=>dx===x && dy===y);
    if (idx!==-1 && rng() > DECORATION_PRESERVE_CHANCE) arr.splice(idx,1);
  }
}

async function placeStreetObjects(location: any, streetAreas: StreetArea[], rng: () => number, carLimit: number): Promise<void> {
  // Собираем множество координат асфальта для быстрого запрета спавна.
  const asphaltSet = new Set<string>();
  if (location.floor && location.floor[TerrainType.ASPHALT]) {
    for (const [ax, ay] of location.floor[TerrainType.ASPHALT]) {
      asphaltSet.add(ax + ":" + ay);
    }
  }
  for (const area of streetAreas) {
    await placeLights(location, area, rng, asphaltSet);
    await placeBenchesAndTrashBins(location, area, rng, asphaltSet);
    await placeManholeCovers(location, area, rng);
    await placeSigns(location, area, rng);
    await placeLitter(location, area, rng);
  }
  await distributeAndPlaceCars(location, streetAreas, carLimit, rng);
}

async function distributeAndPlaceCars(location: any, streetAreas: StreetArea[], carLimit: number, rng: () => number): Promise<void> {
  if (carLimit <= 0) return;
  const candidates: { area: StreetArea; x: number; y: number; horizontal: boolean }[] = [];
  for (const area of streetAreas) {
    const isHorizontal = area.width > area.height;
    if (isHorizontal) {
      for (let x = area.x; x < area.x + area.width; x += CAR_SPAWN_INTERVAL) {
        candidates.push({ area, x, y: area.y, horizontal: true });
      }
    } else {
      for (let y = area.y; y < area.y + area.height; y += CAR_SPAWN_INTERVAL) {
        candidates.push({ area, x: area.x, y, horizontal: false });
      }
    }
  }
  for (let i = candidates.length - 1; i > 0; i--) {
    const j = Math.floor(rng() * (i + 1));
    [candidates[i], candidates[j]] = [candidates[j], candidates[i]];
  }
  let placed = 0;
  for (const c of candidates) {
    if (placed >= carLimit) break;
    placed += await placeCarAtCandidate(location, c.area, c.x, c.y, rng);
  }
}

// Удаление блокирующих уличных декораций при прокладке асфальта
function removeBlockingStreetDecorations(location: any, x: number, y: number) {
  if (!location.decorations) return;
  const blocking = [
    LocationDecorations.BENCH,
    LocationDecorations.EXTERIORLIGHT,
    LocationDecorations.TRASHBIN,
    LocationDecorations.SIGN,
  ];
  for (const type of blocking) {
    const arr = location.decorations[type];
    if (!Array.isArray(arr)) continue;
    for (let i = arr.length - 1; i >= 0; i--) {
      const [dx, dy] = arr[i];
      if (dx === x && dy === y) {
        arr.splice(i, 1);
      }
    }
  }
}
