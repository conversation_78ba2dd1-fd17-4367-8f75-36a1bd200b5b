import React, { useEffect, useState, useRef } from 'react';
import styles from './TextureLoadingScreen.module.css';

interface TextureLoadingScreenProps {
  onComplete: () => void;
}

const TextureLoadingScreen: React.FC<TextureLoadingScreenProps> = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [loaded, setLoaded] = useState(0);
  const [total, setTotal] = useState(0);
  // currentFile / isLoading пока не нужны – держим минимум состояния

  useEffect(() => {
    let unsub: (() => void) | null = null;
    let failSafeTimer: any = null;
    let completed = false;

    const start = async (force = false) => {
      try {
        const { preloadAllTextures, getTotalTextureCount, getTextureInfo, subscribeTexturePreloadProgress, isAllTexturesPreloaded } = await import('../game/rendering/textures/TexturePreloader');
        const info = getTextureInfo();
        const totalCount = getTotalTextureCount();
        setTotal(totalCount);
        const sessionKey = 'texturesPreloadedVersion';
        const currentVersion = info.generatedAt;
        const sessionVersion = sessionStorage.getItem(sessionKey);

        // Подписка на глобальный прогресс
        unsub = subscribeTexturePreloadProgress(p => {
          setLoaded(p.loaded);
          setTotal(p.total);
            setProgress(p.percentage);
          if (!completed && p.percentage >= 100) {
            completed = true;
            sessionStorage.setItem(sessionKey, currentVersion);
            setTimeout(() => onComplete(), 120);
          }
        });

        // Быстрый путь: версия совпала + всё уже в кэше
        if (!force && sessionVersion === currentVersion && isAllTexturesPreloaded()) {
          setProgress(100);
          setLoaded(totalCount);
          completed = true;
          setTimeout(() => onComplete(), 60);
          return;
        }

        // Fail-safe: если 2 секунды прогресс 0 — перезапустить с force
        failSafeTimer = setTimeout(() => {
          if (!completed && (progress === 0 || loaded === 0)) {
            console.warn('[Textures] fail-safe restart');
            start(true);
          }
        }, 2000);

        await preloadAllTextures(undefined, { log: true, force });
      } catch (e) {
        console.error('[Textures] preload error', e);
        setTimeout(() => onComplete(), 400);
      }
    };

    start();
    return () => {
      if (unsub) unsub();
      if (failSafeTimer) clearTimeout(failSafeTimer);
    };
  }, [onComplete]);

  return (
    <div className={styles.loadingScreen}>
      <div className={styles.container}>
        <div className={styles.logo}>
          <h1>Nuclear Story</h1>
          <div className={styles.subtitle}>Загрузка мира...</div>
        </div>
        
        <div className={styles.progressContainer}>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progress}%` }}
            />
          </div>
          
          <div className={styles.progressText}>
            {progress}% ({loaded} / {total})
          </div>
        </div>
        
  {/* Можно добавить имя текущего файла если перейдем на подробный прогресс */}
        
        <div className={styles.loadingAnimation}>
          <div className={styles.spinner}></div>
        </div>
        
        <div className={styles.tips}>
          <p>💡 Совет: Исследуйте каждый уголок мира - вас ждут сюрпризы!</p>
        </div>
      </div>
    </div>
  );
};

export default TextureLoadingScreen;
